import { ResponseInputItem } from 'openai/resources/responses/responses';
import { getIdToken } from '@react-native-firebase/auth';
import { auth } from '@/firebase/firebaseConfig';
import { OpenAIResponse, OutputMessage, TextFormatType } from '@/types/OpenAITypes';

/**
 * Service for interacting with the LLM via Firebase Cloud Functions
 */
export class LlmService {
  private static readonly CLOUD_FUNCTION_URL = 'https://us-central1-chefpal-a9abe.cloudfunctions.net/generate';

  /**
   * Calls the OpenAI API through Firebase Cloud Function
   *
   * @param model The OpenAI model to use
   * @param instructions System instructions for the model
   * @param input Array of input messages for the conversation
   * @param temperature Optional temperature parameter for controlling randomness
   * @param jsonSchema Optional JSON schema for structured responses
   * @param schemaName Optional name for the JSON schema (defaults to 'output' if not provided)
   * @param previousResponseId Optional ID of a previous response to maintain conversation context
   * @returns The response from the OpenAI API
   */
  static async callLlmApi(
    model: string,
    instructions: string,
    input: ResponseInputItem[],
    temperature?: number,
    jsonSchema?: object,
    schemaName?: string,
    previousResponseId?: string
  ): Promise<OpenAIResponse> {
    try {
      // Get the current user ID for tracking and auth purposes
      const idToken = auth.currentUser ? await getIdToken(auth.currentUser) : null;

      let body: any = {
        model,
        instructions,
        input,
        temperature,
      };

      // Add JSON schema if provided
      if (jsonSchema) {
        body.text = {
          format: {
            name: schemaName || 'output',
            schema: jsonSchema,
            type: TextFormatType.json,
          },
        };
      }

      // Add previous response ID if provided
      if (previousResponseId) {
        body.previous_response_id = previousResponseId;
      }

      const response = await fetch(this.CLOUD_FUNCTION_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${idToken}`,
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`API Error: ${errorData.error || response.statusText}`);
      }

      const responseData = await response.json();
      return responseData;
    } catch (error) {
      console.error('LlmService error:', error instanceof Error ? error.message : JSON.stringify(error, null, 2));
      throw error;
    }
  }

  /**
   * Extracts the output text from the OpenAI API response
   *
   * @param response The response from the OpenAI API
   * @returns The extracted output text
   */
  static extractOutputText(response: OpenAIResponse): string {
    // Check if SDK convenience property is available
    if (response.output_text) {
      return response.output_text;
    }

    // Handle the OpenAI API response structure with output array
    if (
      response.output &&
      Array.isArray(response.output) &&
      response.output.length > 0 &&
      response.output[0].type === 'message'
    ) {
      const message = response.output[0] as OutputMessage;
      if (
        message.content &&
        Array.isArray(message.content) &&
        message.content.length > 0 &&
        message.content[0].type === 'output_text'
      ) {
        return message.content[0].text;
      }
    }

    // Return empty string if nothing found
    return '';
  }
}
